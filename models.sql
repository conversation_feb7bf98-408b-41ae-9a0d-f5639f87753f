CREATE TABLE [dbo].[article_property](
	[art_id] [int] NOT NULL,
	[propertykeyvalue_id] [int] NOT NULL,
	[sort] [int] NOT NULL,
 CONSTRAINT [pk_article_property] PRIMARY KEY CLUSTERED 
(
	[art_id] ASC,
	[propertykeyvalue_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

--=======================================================================
CREATE TABLE [dbo].[campaign_property](
	[camp_id] [int] NOT NULL,
	[propertykeyvalue_id] [int] NOT NULL,
	[sort] [int] NOT NULL,
 CONSTRAINT [pk_campaign_property] PRIMARY KEY CLUSTERED 
(
	[camp_id] ASC,
	[propertykeyvalue_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

--=======================================================================

CREATE TABLE [dbo].[propertykey](
	[propertykey_id] [int] IDENTITY(1,1) NOT NULL,
	[propertykey_name] [nvarchar](100) NOT NULL,
	[sort] [int] NOT NULL,
	[keytype] [smallint] NOT NULL,
 CONSTRAINT [pk_propertykey] PRIMARY KEY CLUSTERED 
(
	[propertykey_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

--=======================================================================

CREATE TABLE [dbo].[propertykey_name](
	[propertykey_id] [int] NOT NULL,
	[lang_id] [smallint] NOT NULL,
	[propertykey_name] [nvarchar](100) NOT NULL,
	[propertykey_desc] [nvarchar](max) NOT NULL,
 CONSTRAINT [pk_propertykey_name] PRIMARY KEY CLUSTERED 
(
	[propertykey_id] ASC,
	[lang_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
--=======================================================================
CREATE TABLE [dbo].[propertyvalue](
	[propertyvalue_id] [int] IDENTITY(1,1) NOT NULL,
	[propertyvalue] [nvarchar](max) NOT NULL,
 CONSTRAINT [pk_propertyvalue] PRIMARY KEY CLUSTERED 
(
	[propertyvalue_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
--=======================================================================
CREATE TABLE [dbo].[propertyvalue_name](
	[propertyvalue_id] [int] NOT NULL,
	[lang_id] [smallint] NOT NULL,
	[propertyvalue] [nvarchar](max) NOT NULL,
	[propertyvalue_desc] [nvarchar](max) NOT NULL,
 CONSTRAINT [pk_propertyvalue_name] PRIMARY KEY CLUSTERED 
(
	[propertyvalue_id] ASC,
	[lang_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
--=======================================================================
CREATE TABLE [dbo].[propertykey_value](
	[propertykeyvalue_id] [int] IDENTITY(1,1) NOT NULL,
	[propertykey_id] [int] NOT NULL,
	[propertyvalue_id] [int] NOT NULL,
 CONSTRAINT [pk_propertykey_value] PRIMARY KEY CLUSTERED 
(
	[propertykeyvalue_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO